apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: test-spring-backend
  namespace: argocd
  labels:
    app.kubernetes.io/name: test-spring-backend
    app.kubernetes.io/component: application
    app.kubernetes.io/part-of: test-spring-backend
    environment: dev
    app-type: springboot-backend
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  project: test-spring-backend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: test-spring-backend/k8s/overlays/dev
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: test-spring-backend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
      - RespectIgnoreDifferences=true
      - ApplyOutOfSyncOnly=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 3
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  - group: autoscaling
    kind: HorizontalPodAutoscaler
    jsonPointers:
    - /spec/targetRef/apiVersion
  - group: ""
    kind: ConfigMap
    jsonPointers:
    - /data/deployment-timestamp
  info:
  - name: Description
    value: "test-spring-backend - Spring Boot Backend API"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "dev"
  - name: Target Cluster
    value: "6be4e15d-52f9-431d-84ec-ec8cad0dff2d (Dev/Staging)"
  - name: Application Type
    value: "springboot-backend"
  - name: Deployment Strategy
    value: "Rolling Fast (50% unavailable, 50% surge)"
  - name: Configuration
    value: "Database integration, JWT auth, health checks"
  - name: Resource Limits
    value: "CPU: 100m-500m, Memory: 128Mi-512Mi"
