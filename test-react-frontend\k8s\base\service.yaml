apiVersion: v1
kind: Service
metadata:
  name: test-react-frontend
  namespace: test-react-frontend-dev
  labels:
    app: test-react-frontend
    component: service
    environment: dev
    app-type: react-frontend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.kubernetes.io/managed-by: "gitops-argocd"
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: test-react-frontend
    app.kubernetes.io/name: test-react-frontend
  sessionAffinity: None
  # Service-specific configurations for different environments
