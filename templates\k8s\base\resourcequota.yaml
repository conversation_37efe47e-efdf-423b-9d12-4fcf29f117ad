apiVersion: v1
kind: ResourceQuota
metadata:
  name: {{PROJECT_ID}}-quota
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: resource-quota
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
  annotations:
    resource.kubernetes.io/environment: "{{ENVIRONMENT}}"
    resource.kubernetes.io/managed-by: "gitops-argocd"
spec:
  # Environment-specific resource quotas optimized for 4 vCPU/8GB cluster capacity
  hard:
    {{#eq ENVIRONMENT 'dev'}}
    # Development Environment - Conservative resource allocation
    requests.cpu: "1000m"      # 1 CPU core total
    requests.memory: "1Gi"     # 1GB memory total
    limits.cpu: "2000m"        # 2 CPU cores max
    limits.memory: "2Gi"       # 2GB memory max
    pods: "10"                 # Maximum 10 pods
    persistentvolumeclaims: "5"
    services: "5"
    secrets: "10"
    configmaps: "10"
    {{/eq}}

    {{#eq ENVIRONMENT 'staging'}}
    # Staging Environment - Balanced resource allocation
    requests.cpu: "1500m"      # 1.5 CPU cores total
    requests.memory: "2Gi"     # 2GB memory total
    limits.cpu: "3000m"        # 3 CPU cores max
    limits.memory: "4Gi"       # 4GB memory max
    pods: "15"                 # Maximum 15 pods
    persistentvolumeclaims: "8"
    services: "8"
    secrets: "15"
    configmaps: "15"
    {{/eq}}

    {{#eq ENVIRONMENT 'production'}}
    # Production Environment - Optimized for 4 vCPU/8GB cluster with safety margin
    requests.cpu: "2000m"      # 2 CPU cores total
    requests.memory: "4Gi"     # 4GB memory total
    limits.cpu: "3000m"        # 3 CPU cores max (75% of cluster capacity)
    limits.memory: "6Gi"       # 6GB memory max (75% of cluster capacity)
    pods: "20"                 # Maximum 20 pods
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"
    {{/eq}}

    # Common resource limits across all environments
    replicationcontrollers: "0"  # Use Deployments instead
    resourcequotas: "1"
    count/deployments.apps: "5"
    count/replicasets.apps: "10"
    count/statefulsets.apps: "3"
    count/jobs.batch: "10"
    count/cronjobs.batch: "5"
    count/horizontalpodautoscalers.autoscaling: "5"
    count/poddisruptionbudgets.policy: "5"
    count/networkpolicies.networking.k8s.io: "10"

  # Scope selectors for better resource management
  scopeSelector:
    matchExpressions:
    - operator: In
      scopeName: PriorityClass
      values: ["high-priority", "medium-priority", "low-priority"]
