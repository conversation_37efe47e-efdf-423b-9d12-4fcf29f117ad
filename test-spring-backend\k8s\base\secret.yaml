apiVersion: v1
kind: Secret
metadata:
  name: test-spring-backend-secrets
  namespace: test-spring-backend-dev
  labels:
    app: test-spring-backend
    component: secrets
    environment: dev
    app-type: springboot-backend
  annotations:
    config.kubernetes.io/local-config: "true"
type: Opaque
data:
  # Backend Application Secrets
  # Essential Authentication Secrets
  JWT_SECRET: c3VwZXJzZWNyZXRrZXk=
  # Database Credentials
  DB_USER: cG9zdGdyZXM=
  DB_PASSWORD: cGFzc3dvcmQ=
  # SMTP Configuration
  SMTP_USER: ****************************************
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==
  # OAuth2 Configuration
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=
  # Session Secrets
  SESSION_SECRET: ZGVmYXVsdC1zZXNzaW9uLXNlY3JldA==
  # API Keys and External Service Secrets
  ENCRYPTION_KEY: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgZW5jcnlwdGlvbiBrZXk=  # PLACEHOLDER
  # Third-party API Keys
  STRIPE_SECRET_KEY: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgU3RyaXBlIHNlY3JldCBrZXk=  # PLACEHOLDER
  # Monitoring and Analytics
  SENTRY_DSN: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgU2VudHJ5IERTO04=  # PLACEHOLDER
  # External Service Credentials
  REDIS_PASSWORD: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgUmVkaXMgcGFzc3dvcmQ=  # PLACEHOLDER
  # File Storage Credentials
  AWS_ACCESS_KEY_ID: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgQVdTIGFjY2VzcyBrZXk=  # PLACEHOLDER
  AWS_SECRET_ACCESS_KEY: UExBQ0VIT0xERVIgLSBVcGRhdGUgd2l0aCBhY3R1YWwgYmFzZTY0IGVuY29kZWQgQVdTIHNlY3JldCBrZXk=  # PLACEHOLDER
  # Additional secrets from GitHub issue (if provided)
  ZGV2LWRlYnVnLXRva2Vu  # dev-debug-token
