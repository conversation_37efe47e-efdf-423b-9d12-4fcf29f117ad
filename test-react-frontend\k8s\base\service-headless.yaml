# Headless service for React frontend (for service discovery)
apiVersion: v1
kind: Service
metadata:
  name: test-react-frontend-headless
  namespace: test-react-frontend-dev
  labels:
    app: test-react-frontend
    component: headless-service
    environment: dev
    app-type: react-frontend
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: test-react-frontend
    app.kubernetes.io/name: test-react-frontend
