apiVersion: v1
kind: ResourceQuota
metadata:
  name: test-spring-backend-quota
  namespace: test-spring-backend-dev
  labels:
    app: test-spring-backend
    component: resource-quota
    environment: dev
    app-type: springboot-backend
  annotations:
    resource.kubernetes.io/environment: "dev"
    resource.kubernetes.io/managed-by: "gitops-argocd"
spec:
  # Environment-specific resource quotas optimized for 4 vCPU/8GB cluster capacity
  hard:
    # Development Environment - Conservative resource allocation
    requests.cpu: "1000m"      # 1 CPU core total
    requests.memory: "1Gi"     # 1GB memory total
    limits.cpu: "2000m"        # 2 CPU cores max
    limits.memory: "2Gi"       # 2GB memory max
    pods: "10"                 # Maximum 10 pods
    persistentvolumeclaims: "5"
    services: "5"
    secrets: "10"
    configmaps: "10"
    # Common resource limits across all environments
    replicationcontrollers: "0"  # Use Deployments instead
    resourcequotas: "1"
    count/deployments.apps: "5"
    count/replicasets.apps: "10"
    count/statefulsets.apps: "3"
    count/jobs.batch: "10"
    count/cronjobs.batch: "5"
    count/horizontalpodautoscalers.autoscaling: "5"
    count/poddisruptionbudgets.policy: "5"
    count/networkpolicies.networking.k8s.io: "10"
  # Scope selectors for better resource management
  scopeSelector:
    matchExpressions:
    - operator: In
      scopeName: PriorityClass
      values: ["high-priority", "medium-priority", "low-priority"]
