apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: test-spring-backend-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: test-spring-backend-project
    environment: dev
spec:
  description: "test-spring-backend Project for GitOps deployment"
  sourceRepos:
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps.git'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps'
  destinations:
  - namespace: test-spring-backend-dev
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
  - namespace: argocd
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: PersistentVolume
  - group: storage.k8s.io
    kind: StorageClass
  namespaceResourceWhitelist:
  - group: ''
    kind: ConfigMap
  - group: ''
    kind: Secret
  - group: ''
    kind: Service
  - group: ''
    kind: PersistentVolumeClaim
  - group: apps
    kind: Deployment
  - group: batch
    kind: Job
  - group: networking.k8s.io
    kind: Ingress
  roles:
  - name: admin
    description: "Admin access to test-spring-backend project"
    policies:
    - p, proj:test-spring-backend-project:admin, applications, *, test-spring-backend-project/*, allow
    - p, proj:test-spring-backend-project:admin, repositories, *, *, allow
    groups:
    - argocd:admin
