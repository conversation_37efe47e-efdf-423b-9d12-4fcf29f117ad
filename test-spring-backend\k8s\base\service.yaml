apiVersion: v1
kind: Service
metadata:
  name: test-spring-backend
  namespace: test-spring-backend-dev
  labels:
    app: test-spring-backend
    component: service
    environment: dev
    app-type: springboot-backend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.kubernetes.io/managed-by: "gitops-argocd"
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: test-spring-backend
    app.kubernetes.io/name: test-spring-backend
  sessionAffinity: None
  # Service-specific configurations for different environments
