apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
metadata:
  name: test-react-frontend-base
  annotations:
    config.kubernetes.io/local-config: "true"
resources:
  - deployment-rolling.yaml
  - service.yaml
  - service-headless.yaml
  - configmap.yaml
  - secret.yaml
  - resourcequota.yaml
  - limitrange.yaml
commonLabels:
  app: test-react-frontend
  app.kubernetes.io/name: test-react-frontend
  app.kubernetes.io/part-of: test-react-frontend
  app.kubernetes.io/managed-by: argocd
  environment: dev
  app-type: react-frontend
commonAnnotations:
  app.kubernetes.io/version: "1.0.0"
  deployment.kubernetes.io/revision: "1"
namespace: test-react-frontend-dev
images:
  - name: app-image
    newName: test/react-app
    newTag: latest
replicas:
  - name: test-react-frontend
    count: 1
configMapGenerator:
  - name: test-react-frontend-config
    behavior: merge
    literals:
      - ENVIRONMENT=dev
      - APP_NAME=test-react-frontend
      - PROJECT_ID=test-react-frontend
secretGenerator:
  - name: test-react-frontend-secrets
    behavior: merge
    type: Opaque
