apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
metadata:
  name: test-spring-backend-base
  annotations:
    config.kubernetes.io/local-config: "true"
resources:
  - deployment-rolling.yaml
  - service.yaml
  - configmap.yaml
  - secret.yaml
  - resourcequota.yaml
  - limitrange.yaml
  - ../../../k8s/postgres-deployment.yaml
  - ../../../k8s/postgres-service.yaml
  - ../../../k8s/postgres-pvc.yaml
commonLabels:
  app: test-spring-backend
  app.kubernetes.io/name: test-spring-backend
  app.kubernetes.io/part-of: test-spring-backend
  app.kubernetes.io/managed-by: argocd
  environment: dev
  app-type: springboot-backend
commonAnnotations:
  app.kubernetes.io/version: "1.0.0"
  deployment.kubernetes.io/revision: "1"
namespace: test-spring-backend-dev
images:
  - name: app-image
    newName: test/spring-app
    newTag: latest
replicas:
  - name: test-spring-backend
    count: 1
configMapGenerator:
  - name: test-spring-backend-config
    behavior: merge
    literals:
      - ENVIRONMENT=dev
      - APP_NAME=test-spring-backend
      - PROJECT_ID=test-spring-backend
secretGenerator:
  - name: test-spring-backend-secrets
    behavior: merge
    type: Opaque
