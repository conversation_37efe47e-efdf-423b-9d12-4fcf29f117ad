apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-spring-backend
  namespace: test-spring-backend-dev
  labels:
    app: test-spring-backend
    component: springboot-backend
    version: v1.0.0
    environment: dev
  annotations:
    deployment.kubernetes.io/revision: "1"
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test-spring-backend
      app.kubernetes.io/name: test-spring-backend
  # Rolling Update Strategy - Environment Specific (will be patched by overlays)
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  # Progress deadline - Environment Specific (will be patched by overlays)
  progressDeadlineSeconds: 300
  # Revision history limit - Environment Specific (will be patched by overlays)
  revisionHistoryLimit: 5
  template:
    metadata:
      labels:
        app: test-spring-backend
        app.kubernetes.io/name: test-spring-backend
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: springboot-backend
        version: v1.0.0
        environment: dev
      annotations:
        deployment.kubernetes.io/config-hash: "latest"
    spec:
      # Security Context - Environment Specific (will be patched by overlays)
      securityContext:
        runAsNonRoot: false
        fsGroup: 2000
      # Termination Grace Period - Environment Specific (will be patched by overlays)
      terminationGracePeriodSeconds: 30
      # Backend Applications - Database init container
      initContainers:
      - name: wait-for-postgres
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h test-spring-backend-postgres -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: test-spring-backend-secrets
              key: DB_PASSWORD
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
      containers:
      - name: test-spring-backend
        image: app-image  # Will be replaced by Kustomize
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: test-spring-backend-config
        # Backend Applications - Full environment variables
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8080"
        # Database Configuration
        - name: DB_HOST
          value: "test-spring-backend-postgres"
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: "test_spring_backend"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: test-spring-backend-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: test-spring-backend-secrets
              key: DB_PASSWORD
        # Authentication Secrets
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: test-spring-backend-secrets
              key: JWT_SECRET
        # OAuth Configuration
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: test-spring-backend-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: test-spring-backend-secrets
              key: GOOGLE_CLIENT_SECRET
        # Health Checks - Application Type Specific (will be enhanced by overlays)
        startupProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 12
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
        # Resource Management - Environment Specific (will be patched by overlays)
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        # Security Context - Environment Specific (will be patched by overlays)
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: false
          capabilities:
            drop:
            - ALL
        # Volume Mounts for temporary files (if needed)
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: var-cache
          mountPath: /var/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: var-cache
        emptyDir: {}
      # Node Selection and Affinity - Environment Specific (will be patched by overlays)
      nodeSelector: {}
      tolerations: []
      affinity: {}
